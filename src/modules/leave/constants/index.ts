import type { SelectOption } from "@/shared/types/common";

export const LeaveRequestStatus = {
	PENDING: "PENDING",
	APPROVED: "APPROVED",
	REJECTED: "REJECTED",
} as const;

export const LeaveRequestStatusLabel: Record<
	keyof typeof LeaveRequestStatus,
	string
> = {
	PENDING: "Perlu Persetujuan",
	APPROVED: "Disetujui",
	REJECTED: "Ditolak",
};

export const leaveRequestStatusSelectOption: SelectOption[] = [
	{
		label: "Perlu Persetujuan",
		value: "PENDING",
	},
	{
		label: "Disetuju<PERSON>",
		value: "APPROVED",
	},
	{
		label: "<PERSON><PERSON><PERSON>",
		value: "REJECTED",
	},
];
