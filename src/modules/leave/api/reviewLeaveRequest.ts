import { apiClient } from "@/shared/api/apiClient";
import type {
	ReviewLeaveRequestPayload,
	ReviewLeaveRequestResponse,
} from "@/shared/types/api";

export const reviewLeaveRequest = async (
	leaveRequestId: string,
	payload: ReviewLeaveRequestPayload,
) => {
	const result = await apiClient.patch<ReviewLeaveRequestResponse>(
		`/api/v1/admin/leave-requests/${leaveRequestId}/review`,
		payload,
	);
	return result;
};
