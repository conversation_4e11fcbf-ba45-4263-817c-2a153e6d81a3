import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	ReviewLeaveRequestPayload,
	ReviewLeaveRequestResponse,
} from "@/shared/types/api";
import { reviewLeaveRequest } from "../api/reviewLeaveRequest";

interface UseReviewLeaveRequestOptions {
	onSuccessCallback?: (data: ReviewLeaveRequestResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useReviewLeaveRequest = (
	options?: UseReviewLeaveRequestOptions,
) => {
	return useMutation<
		ReviewLeaveRequestResponse,
		AxiosError<BaseErrorResponse>,
		{ leaveRequestId: string; payload: ReviewLeaveRequestPayload }
	>({
		mutationFn: ({ leaveRequestId, payload }) => {
			return reviewLeaveRequest(leaveRequestId, payload);
		},
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
