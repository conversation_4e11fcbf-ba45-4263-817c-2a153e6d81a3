import { <PERSON>, Button, Chip, Stack, Typography } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { Link as ReactRouterLink, useNavigate } from "react-router";
import IconsaxAddSquareIcon from "@/assets/icons/iconsax-add-square.svg?react";
import IconsaxReceiveSquareIcon from "@/assets/icons/iconsax-receive-square.svg?react";
import { DataTable } from "@/shared/components/common/DataTable";
import { ColumnHeader } from "@/shared/components/common/DataTable/ColumnHeader";
import { RowActions } from "@/shared/components/common/DataTable/RowActions";
import { ViewOptions } from "@/shared/components/common/DataTable/ViewOption";
import { Footer } from "@/shared/components/common/Footer";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { useBulkActionHandler } from "@/shared/hooks/useBulkActionHandler";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { OfficeLeaveResponse } from "@/shared/types/api";
import type { SelectionData } from "@/shared/types/common";
import { getAllOfficeLeave } from "../api/getAllOfficeLeave";
import {
	OfficeLeaveStatus,
	OfficeLeaveStatusLabel,
	officeLeaveStatusSelectOption,
} from "../constants";
import { useBulkActionOfficeLeave } from "../hooks/useBulkActionOfficeLeave";
import { useMarkOfficeLeave } from "../hooks/useMarkOfficeLeave";

const OFFICE_LEAVES_QUERY_KEY = ["getAllOfficeLeave"];

const OfficeLeaveListPage: React.FC = () => {
	const navigate = useNavigate();
	const [selectionData, setSelectionData] = useState<SelectionData>({
		selectedIds: [],
		excludedIds: [],
		isSelectAll: false,
		selectionInfo: { selectedCount: 0, totalCount: 0, isSelectAll: false },
	});

	const columns: ColumnDef<OfficeLeaveResponse>[] = [
		{
			accessorKey: "userName",
			header: ({ column }) => <ColumnHeader column={column} title="Nama" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Nama", filterVariant: "textSearch" },
		},
		{
			accessorKey: "userEmail",
			header: ({ column }) => <ColumnHeader column={column} title="Email" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Email", filterVariant: "textSearch" },
		},
		{
			accessorKey: "title",
			header: ({ column }) => (
				<ColumnHeader sx={{ minWidth: 200 }} column={column} title="Judul" />
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: false,
			enableSorting: false,
			meta: { columnLabel: "Judul" },
		},
		{
			accessorKey: "description",
			header: ({ column }) => (
				<ColumnHeader
					sx={{ minWidth: 200 }}
					column={column}
					title="Description"
				/>
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: false,
			enableSorting: false,
			meta: { columnLabel: "Description" },
		},
		{
			accessorKey: "status",
			header: ({ column }) => <ColumnHeader column={column} title="Status" />,
			cell: (info) => {
				const status = info.getValue();

				return (
					<Chip
						label={
							OfficeLeaveStatusLabel[
								status as keyof typeof OfficeLeaveStatusLabel
							]
						}
						color={
							status === OfficeLeaveStatus.NEED_REVIEW ? "primary" : "success"
						}
						size="small"
					/>
				);
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: {
				columnLabel: "Status",
				filterVariant: "select",
				selectOptions: officeLeaveStatusSelectOption,
			},
		},
		{
			accessorKey: "date",
			header: ({ column }) => <ColumnHeader column={column} title="Tanggal" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Tanggal", filterVariant: "dateRange" },
		},
		{
			accessorKey: "startTime",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Jam Keluar" />
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: false,
			enableSorting: false,
			meta: { columnLabel: "Jam Keluar" },
		},
		{
			accessorKey: "endTime",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Jam Kembali" />
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: false,
			enableSorting: false,
			meta: { columnLabel: "Jam Kembali" },
		},
		{
			accessorKey: "reviewerName",
			header: ({ column }) => <ColumnHeader column={column} title="Reviewer" />,
			cell: (info) => {
				const value = info.getValue();

				if (value === null) {
					return "-";
				}

				return value;
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Reviewer", filterVariant: "textSearch" },
		},
		{
			accessorKey: "reviewerEmail",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Reviewer Email" />
			),
			cell: (info) => {
				const value = info.getValue();

				if (value === null) {
					return "-";
				}

				return value;
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Reviewer Email", filterVariant: "textSearch" },
		},
		{
			accessorKey: "isOfficialBusiness",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Izin pekerjaan?" />
			),
			cell: (info) => {
				const value = info.getValue();

				if (value === null) {
					return "-";
				}

				return (
					<Chip
						label={value ? "Ya" : "Tidak"}
						color={value ? "success" : "default"}
						size="small"
					/>
				);
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: {
				columnLabel: "Izin perkerjaan",
				filterVariant: "select",
				selectOptions: [
					{
						label: "Ya",
						value: "true",
					},
					{
						label: "Tidak",
						value: "false",
					},
				],
			},
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => <ColumnHeader column={column} title="Dibuat" />,
			cell: (info) => {
				const value = info.getValue() as string;
				return new Date(value).toLocaleDateString("id-ID");
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Dibuat", filterVariant: "dateRange" },
		},
	];

	const queryClient = useQueryClient();
	const { handleSuccess, handleError } = useMutationCallbacks();
	const bulkActionMutation = useBulkActionOfficeLeave({
		onSuccessCallback: (data) => {
			if (data instanceof Blob) {
				// Download CSV
				const url = window.URL.createObjectURL(data);
				const a = document.createElement("a");
				a.href = url;
				a.download = "exported_office_leaves.csv";
				document.body.appendChild(a);
				a.click();
				a.remove();
				window.URL.revokeObjectURL(url);
			} else {
				handleSuccess(data.message);
			}
		},
		onErrorCallback: handleError,
	});

	const markMutation = useMarkOfficeLeave({
		onSuccessCallback: async (data) => {
			handleSuccess(data.message);
			await queryClient.invalidateQueries({
				queryKey: OFFICE_LEAVES_QUERY_KEY,
			});
		},
		onErrorCallback: handleError,
	});

	const handleBulkAction = useBulkActionHandler(
		bulkActionMutation,
		selectionData,
	);

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Manajemen Izin Keluar
			</Typography>
			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflow: "auto",
				}}
			>
				<DataTable
					queryKey={OFFICE_LEAVES_QUERY_KEY}
					columns={columns}
					fetchData={getAllOfficeLeave}
					enableSelection
					onSelectionChange={setSelectionData}
					renderRowActions={(row) => {
						return (
							<RowActions
								row={row}
								onEdit={(data) => navigate(`/office-leaves/${data.id}/edit`)}
								viewTitle="Detail Izin Keluar"
								renderDetail={(data) => (
									<>
										<Stack direction="column" spacing={2} sx={{ mt: 1 }}>
											<Stack
												direction="column"
												spacing={2}
												sx={{ mt: 1, textAlign: "right" }}
											>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">
														Nama kariawan
													</Typography>
													<Typography>{data.userName}</Typography>
												</Stack>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">
														Email kariawan
													</Typography>
													<Typography>{data.userEmail}</Typography>
												</Stack>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">Judul</Typography>
													<Typography>{data.title}</Typography>
												</Stack>
												<Stack direction="column" textAlign="left">
													<Typography color="textDisabled">
														Deskripsi
													</Typography>
													<Typography>{data.description}</Typography>
												</Stack>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">Status</Typography>
													<Box>
														<Chip
															label={
																OfficeLeaveStatusLabel[
																	data.status as keyof typeof OfficeLeaveStatusLabel
																]
															}
															color={
																data.status === OfficeLeaveStatus.NEED_REVIEW
																	? "primary"
																	: "success"
															}
															size="small"
														/>
													</Box>
												</Stack>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">Tanggal</Typography>
													<Typography>{data.date}</Typography>
												</Stack>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">
														Jam Keluar
													</Typography>
													<Typography>{data.startTime}</Typography>
												</Stack>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">
														Jam Kembali
													</Typography>
													<Typography>{data.endTime}</Typography>
												</Stack>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">Reviewer</Typography>
													<Typography>{data.reviewerName ?? "-"}</Typography>
												</Stack>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">
														Reviewer Email
													</Typography>
													<Typography>{data.reviewerEmail ?? "-"}</Typography>
												</Stack>
												<Stack direction="row" justifyContent="space-between">
													<Typography color="textDisabled">
														Izin Perkerjaan?
													</Typography>
													<Typography>
														{data.isOfficialBusiness === null
															? "-"
															: data.isOfficialBusiness
																? "Ya"
																: "Tidak"}
													</Typography>
												</Stack>
												{data.status === "NEED_REVIEW" && (
													<Stack direction="row" spacing={2}>
														<Button
															onClick={() => {
																markMutation.mutate({
																	officeLeaveId: data.id,
																	payload: {
																		isOfficialBusiness: true,
																	},
																});
															}}
															loading={markMutation.isPending}
															fullWidth
															variant="contained"
															color="success"
														>
															Valid
														</Button>
														<Button
															onClick={() => {
																markMutation.mutate({
																	officeLeaveId: data.id,
																	payload: {
																		isOfficialBusiness: false,
																	},
																});
															}}
															loading={markMutation.isPending}
															fullWidth
															variant="contained"
															color="error"
														>
															Tidak Valid
														</Button>
													</Stack>
												)}
											</Stack>
										</Stack>
									</>
								)}
							/>
						);
					}}
					toolbar={({ hideableColumns, onDeselectAll }) => {
						return (
							<Stack
								direction="row"
								alignItems="center"
								justifyContent="space-between"
								sx={{ mb: 4 }}
							>
								<Box>
									<Typography variant="subtitle1">
										Tabel Manajemen Izin Keluar
									</Typography>
								</Box>
								<Stack direction="row" spacing={2} alignItems="center">
									<Button
										component={ReactRouterLink}
										to="/office-leaves/new"
										variant="contained"
										color="primary"
										startIcon={<IconWrapper icon={IconsaxAddSquareIcon} />}
									>
										Tambah Data
									</Button>
									<Button
										variant="outlined"
										color="inherit"
										startIcon={<IconWrapper icon={IconsaxReceiveSquareIcon} />}
										onClick={() => handleBulkAction("export", onDeselectAll)}
									>
										Export Data
									</Button>
									<ViewOptions hideableColumns={hideableColumns} />
								</Stack>
							</Stack>
						);
					}}
				/>
			</Box>
			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};

export default OfficeLeaveListPage;
