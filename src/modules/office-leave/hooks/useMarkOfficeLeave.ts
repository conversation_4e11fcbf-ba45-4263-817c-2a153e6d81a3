import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type {
	BaseErrorResponse,
	MarkOfficeLeavePayload,
	MarkOfficeLeaveResponse,
} from "@/shared/types/api";
import { markOfficeLeave } from "../api/markOfficeLeave";

interface UseMarkOfficeLeaveOptions {
	onSuccessCallback?: (data: MarkOfficeLeaveResponse) => void;
	onErrorCallback?: (error: AxiosError<BaseErrorResponse>) => void;
}

export const useMarkOfficeLeave = (options?: UseMarkOfficeLeaveOptions) => {
	return useMutation<
		MarkOfficeLeaveResponse,
		AxiosError<BaseErrorResponse>,
		{ officeLeaveId: string; payload: MarkOfficeLeavePayload }
	>({
		mutationFn: async ({ officeLeaveId, payload }) => {
			return markOfficeLeave(officeLeaveId, payload);
		},
		onSuccess: async (data) => {
			options?.onSuccessCallback?.(data);
		},
		onError: (error) => {
			options?.onErrorCallback?.(error);
		},
	});
};
