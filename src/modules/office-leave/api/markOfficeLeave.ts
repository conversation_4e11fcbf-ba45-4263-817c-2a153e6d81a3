import { apiClient } from "@/shared/api/apiClient";
import type {
	MarkOfficeLeavePayload,
	MarkOfficeLeaveResponse,
} from "@/shared/types/api";

export const markOfficeLeave = async (
	officeLeaveId: string,
	payload: MarkOfficeLeavePayload,
) => {
	const result = await apiClient.patch<MarkOfficeLeaveResponse>(
		`/api/v1/admin/office-leaves/${officeLeaveId}/mark`,
		payload,
	);
	return result;
};
