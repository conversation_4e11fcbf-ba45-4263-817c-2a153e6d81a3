import { <PERSON>, Button, Chip, <PERSON>ack, Typography } from "@mui/material";
import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import IconsaxReceiveSquareIcon from "@/assets/icons/iconsax-receive-square.svg?react";
import { DataTable } from "@/shared/components/common/DataTable";
import { ColumnHeader } from "@/shared/components/common/DataTable/ColumnHeader";
import { RowActions } from "@/shared/components/common/DataTable/RowActions";
import { ViewOptions } from "@/shared/components/common/DataTable/ViewOption";
import { Footer } from "@/shared/components/common/Footer";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { useBulkActionHandler } from "@/shared/hooks/useBulkActionHandler";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { KpiResponse } from "@/shared/types/api";
import type { SelectionData } from "@/shared/types/common";
import { getAllKpiRecord } from "../api/getAllKpiRecord";
import { kpiScoreRangeSelectOption } from "../constants";
import { useBulkActionKpiRecord } from "../hooks/useBulkActionKpiRecord";

const KPI_RECORDS_QUERY_KEY = ["getAllKpiRecord"];

const KpiRecordListPage: React.FC = () => {
	const [selectionData, setSelectionData] = useState<SelectionData>({
		selectedIds: [],
		excludedIds: [],
		isSelectAll: false,
		selectionInfo: { selectedCount: 0, totalCount: 0, isSelectAll: false },
	});

	const getKpiScoreColor = (score: number) => {
		if (score >= 90) return "success";
		if (score >= 80) return "primary";
		if (score >= 70) return "warning";
		if (score >= 60) return "error";
		return "default";
	};

	const getKpiScoreLabel = (score: number) => {
		if (score >= 90) return "Sangat Baik";
		if (score >= 80) return "Baik";
		if (score >= 70) return "Cukup";
		if (score >= 60) return "Kurang";
		return "Sangat Kurang";
	};

	const columns: ColumnDef<KpiResponse>[] = [
		{
			accessorKey: "userName",
			header: ({ column }) => <ColumnHeader column={column} title="Nama" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Nama", filterVariant: "textSearch" },
		},
		{
			accessorKey: "userEmail",
			header: ({ column }) => <ColumnHeader column={column} title="Email" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Email", filterVariant: "textSearch" },
		},
		{
			accessorKey: "period",
			header: ({ column }) => <ColumnHeader column={column} title="Periode" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Periode", filterVariant: "textSearch" },
		},
		{
			accessorKey: "finalKpiScore",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Skor KPI Final" />
			),
			cell: (info) => {
				const score = info.getValue() as number;
				return (
					<Chip
						label={`${score.toFixed(2)} - ${getKpiScoreLabel(score)}`}
						color={getKpiScoreColor(score)}
						size="small"
					/>
				);
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: {
				columnLabel: "Skor KPI Final",
				filterVariant: "select",
				selectOptions: kpiScoreRangeSelectOption,
			},
		},
		{
			accessorKey: "taskAverageScore",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Rata-rata Tugas" />
			),
			cell: (info) => {
				const score = info.getValue() as number;
				return score.toFixed(2);
			},
			enableColumnFilter: false,
			enableSorting: true,
			meta: { columnLabel: "Rata-rata Tugas" },
		},
		{
			accessorKey: "attendancePercentage",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Persentase Kehadiran" />
			),
			cell: (info) => {
				const percentage = info.getValue() as number;
				return `${percentage.toFixed(2)}%`;
			},
			enableColumnFilter: false,
			enableSorting: true,
			meta: { columnLabel: "Persentase Kehadiran" },
		},
		{
			accessorKey: "violationAverageScore",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Rata-rata Pelanggaran" />
			),
			cell: (info) => {
				const score = info.getValue() as number;
				return score.toFixed(2);
			},
			enableColumnFilter: false,
			enableSorting: true,
			meta: { columnLabel: "Rata-rata Pelanggaran" },
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => <ColumnHeader column={column} title="Dibuat" />,
			cell: (info) => {
				const value = info.getValue() as string;
				return new Date(value).toLocaleDateString("id-ID");
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Dibuat", filterVariant: "dateRange" },
		},
	];

	const { handleSuccess, handleError } = useMutationCallbacks();
	const bulkActionMutation = useBulkActionKpiRecord({
		onSuccessCallback: (data) => {
			if (data instanceof Blob) {
				// Download CSV
				const url = window.URL.createObjectURL(data);
				const a = document.createElement("a");
				a.href = url;
				a.download = "exported_kpi_records.csv";
				document.body.appendChild(a);
				a.click();
				a.remove();
				window.URL.revokeObjectURL(url);
			} else {
				handleSuccess(data.message);
			}
		},
		onErrorCallback: handleError,
	});

	const handleBulkAction = useBulkActionHandler(
		bulkActionMutation,
		selectionData,
	);

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Manajemen KPI Record
			</Typography>
			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflow: "auto",
				}}
			>
				<DataTable
					queryKey={KPI_RECORDS_QUERY_KEY}
					columns={columns}
					fetchData={getAllKpiRecord}
					enableSelection
					onSelectionChange={setSelectionData}
					renderRowActions={(row) => (
						<RowActions
							row={row}
							viewTitle="Detail KPI Record"
							renderDetail={(data) => (
								<>
									<Stack direction="column" spacing={2} sx={{ mt: 1 }}>
										<Stack
											direction="column"
											spacing={2}
											sx={{ mt: 1, textAlign: "right" }}
										>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Nama Karyawan
												</Typography>
												<Typography>{data.userName}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Email Karyawan
												</Typography>
												<Typography>{data.userEmail}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Periode</Typography>
												<Typography>{data.period}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Skor KPI Final
												</Typography>
												<Box>
													<Chip
														label={`${data.finalKpiScore.toFixed(2)} - ${getKpiScoreLabel(data.finalKpiScore)}`}
														color={getKpiScoreColor(data.finalKpiScore)}
														size="small"
													/>
												</Box>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Rata-rata Skor Tugas
												</Typography>
												<Typography>
													{data.taskAverageScore.toFixed(2)}
												</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Persentase Kehadiran
												</Typography>
												<Typography>
													{data.attendancePercentage.toFixed(2)}%
												</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Rata-rata Skor Pelanggaran
												</Typography>
												<Typography>
													{data.violationAverageScore.toFixed(2)}
												</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Total Tugas
												</Typography>
												<Typography>{data.totalTasks}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Total Hari Kerja
												</Typography>
												<Typography>{data.totalWorkDays}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Total Hari Kehadiran
												</Typography>
												<Typography>{data.totalAttendanceDays}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Total Pelanggaran
												</Typography>
												<Typography>{data.totalViolations}</Typography>
											</Stack>
										</Stack>
									</Stack>
								</>
							)}
						/>
					)}
					toolbar={({ hideableColumns, onDeselectAll }) => {
						return (
							<Stack
								direction="row"
								alignItems="center"
								justifyContent="space-between"
								sx={{ mb: 4 }}
							>
								<Box>
									<Typography variant="subtitle1">
										Tabel Manajemen KPI Record
									</Typography>
								</Box>
								<Stack direction="row" spacing={2} alignItems="center">
									<Button
										variant="outlined"
										color="inherit"
										startIcon={<IconWrapper icon={IconsaxReceiveSquareIcon} />}
										onClick={() => handleBulkAction("export", onDeselectAll)}
									>
										Export Data
									</Button>
									<ViewOptions hideableColumns={hideableColumns} />
								</Stack>
							</Stack>
						);
					}}
				/>
			</Box>
			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};

export default KpiRecordListPage;
