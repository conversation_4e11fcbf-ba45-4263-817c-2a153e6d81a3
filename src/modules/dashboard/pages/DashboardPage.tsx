import { Box, Grid } from "@mui/material";
import { useNavigate } from "react-router";
import IconsaxTimerStartIcon from "@/assets/icons/iconsax-timer-start.svg?react";
import IconsaxUserDoubleIcon from "@/assets/icons/iconsax-user-double.svg?react";
import FullPageLoader from "@/shared/components/common/FullPageLoader";
import { AttendanceLineChart } from "../components/AttendanceLineChart";
import { CurrentActionableItems } from "../components/CurrentActionableItems";
import { CustomCard } from "../components/CustomCard";
import { useGetDashboardData } from "../hooks/useDashboardData";

const iconMap = {
	users_double_blue: {
		component: IconsaxUserDoubleIcon,
		contrastText: "#0090FF",
		background: "#C3E6FF",
	},
	users_double_green: {
		component: IconsaxUserDoubleIcon,
		contrastText: "#00CE11",
		background: "#C9EACB",
	},
	users_double_red: {
		component: IconsaxUserDoubleIcon,
		contrastText: "#F02D2D",
		background: "#FFB8B8",
	},
	users_double_yellow: {
		component: IconsaxUserDoubleIcon,
		contrastText: "#FFC800",
		background: "#FFEEB0",
	},
	timer_green: {
		component: IconsaxTimerStartIcon,
		contrastText: "#00CE11",
		background: "#C9EACB",
	},
	timer_red: {
		component: IconsaxTimerStartIcon,
		contrastText: "#F02D2D",
		background: "#FFB8B8",
	},
	timer_blue: {
		component: IconsaxTimerStartIcon,
		contrastText: "#0090FF",
		background: "#C3E6FF",
	},
	timer_yellow: {
		component: IconsaxTimerStartIcon,
		contrastText: "#FFC800",
		background: "#FFEEB0",
	},
} as const;

const trendColorMap = {
	increase: { contrastText: "#00CE11", background: "#C9EACB", symbol: "▲" },
	decrease: { contrastText: "#F02D2D", background: "#FFB8B8", symbol: "▼" },
	neutral: { contrastText: "#666666", background: "#E8E9E9", symbol: "~" },
} as const;

const DashboardPage: React.FC = () => {
	const navigate = useNavigate();
	const { data: dashboardData, isLoading } = useGetDashboardData();

	if (isLoading) return <FullPageLoader />;

	const usersData =
		dashboardData?.data.usersData.map((item) => ({
			...item,
			icon: iconMap[item.iconKey as keyof typeof iconMap],
			trend: {
				...item.trend,
				...trendColorMap[item.trend.trendType],
			},
		})) || [];

	const attendanceData =
		dashboardData?.data.attendanceData.map((item) => ({
			...item,
			icon: iconMap[item.iconKey as keyof typeof iconMap],
			trend: {
				...item.trend,
				...trendColorMap[item.trend.trendType],
			},
		})) || [];

	const leaveRequests = dashboardData?.data.latestLeaveRequests ?? [];
	const officeLeaves = dashboardData?.data.latestOfficeLeaves ?? [];

	return (
		<Box sx={{ pb: 12 }}>
			<Grid container spacing={2}>
				{usersData.map((item) => (
					<Grid key={item.id} size={{ xs: 12, md: 6, lg: 3 }}>
						<CustomCard {...item} />
					</Grid>
				))}
			</Grid>

			<Box sx={{ mt: 4 }}>
				<AttendanceLineChart />
			</Box>

			<Grid container spacing={2} sx={{ mt: 4 }}>
				{attendanceData.map((item) => (
					<Grid key={item.id} size={{ xs: 12, md: 6, lg: 3 }}>
						<CustomCard {...item} />
					</Grid>
				))}
			</Grid>

			<Grid container spacing={2} sx={{ mt: 4 }}>
				<Grid size={{ xs: 12, md: 6 }}>
					<CurrentActionableItems
						label="Pengajuan Cuti"
						items={leaveRequests}
						onViewAll={() => navigate("/leaves")}
					/>
				</Grid>
				<Grid size={{ xs: 12, md: 6 }}>
					<CurrentActionableItems
						label="Pengajuan Izin Keluar"
						items={officeLeaves}
						onViewAll={() => navigate("/office-leaves")}
					/>
				</Grid>
			</Grid>
		</Box>
	);
};

export default DashboardPage;
