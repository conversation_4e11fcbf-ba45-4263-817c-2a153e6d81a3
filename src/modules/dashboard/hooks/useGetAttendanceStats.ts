import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type { GetAttendanceStatsResponse } from "@/shared/types/api";
import { getAttendanceStats } from "../api/getAttendanceStats";

export const useGetAttendanceStats = (
	startDate?: string,
	endDate?: string,
	options?: Omit<
		UseQueryOptions<GetAttendanceStatsResponse, AxiosError>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["attendanceStats", startDate, endDate],
		queryFn: async () => getAttendanceStats(startDate, endDate),
		...options,
	});
};
