import { type UseQueryOptions, useQuery } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type { GetDashboardDataResponse } from "@/shared/types/api";
import { getDashboardData } from "../api/getDashboardData";

export const useGetDashboardData = (
	options?: Omit<
		UseQueryOptions<GetDashboardDataResponse, AxiosError>,
		"queryKey" | "queryFn"
	>,
) => {
	return useQuery({
		queryKey: ["dashboardData"],
		queryFn: async () => getDashboardData(),
		...options,
	});
};
