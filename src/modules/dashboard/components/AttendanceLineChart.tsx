import { Card, CardContent, Skeleton, Stack, Typography } from "@mui/material";
import { LineChart } from "@mui/x-charts/LineChart";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import type { Dayjs } from "dayjs";
import * as React from "react";
import { useGetAttendanceStats } from "../hooks/useGetAttendanceStats";

// Custom Date Range Picker Component
interface DateRangePickerProps {
	startDate: Dayjs | null;
	endDate: Dayjs | null;
	onStartDateChange: (date: Dayjs | null) => void;
	onEndDateChange: (date: Dayjs | null) => void;
}

function CustomDateRangePicker({
	startDate,
	endDate,
	onStartDateChange,
	onEndDateChange,
}: DateRangePickerProps) {
	return (
		<LocalizationProvider dateAdapter={AdapterDayjs}>
			<Stack direction="row" spacing={2} alignItems="center">
				<DatePicker
					label="Tanggal Mulai"
					value={startDate}
					onChange={onStartDateChange}
					maxDate={endDate || undefined}
					slotProps={{
						textField: {
							size: "small",
							sx: { minWidth: 150 },
						},
					}}
				/>
				<Typography variant="body2" color="text.secondary">
					sampai
				</Typography>
				<DatePicker
					label="Tanggal Akhir"
					value={endDate}
					onChange={onEndDateChange}
					minDate={startDate || undefined}
					slotProps={{
						textField: {
							size: "small",
							sx: { minWidth: 150 },
						},
					}}
				/>
			</Stack>
		</LocalizationProvider>
	);
}

export function AttendanceLineChart() {
	const [startDate, setStartDate] = React.useState<Dayjs | null>(null);
	const [endDate, setEndDate] = React.useState<Dayjs | null>(null);

	const startDateFormatted = startDate?.format("YYYY-MM-DD") ?? "";
	const endDateFormatted = endDate?.format("YYYY-MM-DD") ?? "";

	const { data: attendanceStatsData, isLoading } = useGetAttendanceStats(
		startDateFormatted,
		endDateFormatted,
	);

	const xLabels =
		attendanceStatsData?.data?.map((item) =>
			new Date(item.date).toLocaleDateString("id-ID", {
				weekday: "short",
				day: "2-digit",
				month: "2-digit",
				year: "numeric",
			}),
		) ?? [];

	const totalData = attendanceStatsData?.data?.map((item) => item.total) ?? [];
	const presentData =
		attendanceStatsData?.data?.map((item) => item.present) ?? [];
	const absentData =
		attendanceStatsData?.data?.map((item) => item.absent) ?? [];
	const leaveData = attendanceStatsData?.data?.map((item) => item.leave) ?? [];

	const handleStartDateChange = (newDate: Dayjs | null) => {
		setStartDate(newDate);
		// If start date is after end date, reset end date
		if (newDate && endDate && newDate.isAfter(endDate)) {
			setEndDate(newDate);
		}
	};

	const handleEndDateChange = (newDate: Dayjs | null) => {
		setEndDate(newDate);
		// If end date is before start date, reset start date
		if (newDate && startDate && newDate.isBefore(startDate)) {
			setStartDate(newDate);
		}
	};

	return (
		<Card>
			<CardContent>
				<Stack
					direction="row"
					justifyContent="space-between"
					alignItems="center"
					mb={2}
				>
					<Typography variant="h6">Statistik Kehadiran Karyawan</Typography>
					<CustomDateRangePicker
						startDate={startDate}
						endDate={endDate}
						onStartDateChange={handleStartDateChange}
						onEndDateChange={handleEndDateChange}
					/>
				</Stack>

				{isLoading ? (
					<Skeleton variant="rectangular" height={300} />
				) : (
					<LineChart
						height={350}
						series={[
							{
								data: totalData,
								label: "Total Karyawan",
								color: "#2F80ED",
								curve: "linear",
							},
							{
								data: presentData,
								label: "Hadir",
								color: "#27AE60",
								curve: "linear",
							},
							{
								data: absentData,
								label: "Tidak Hadir",
								color: "#EB5757",
								curve: "linear",
							},
							{
								data: leaveData,
								label: "Cuti",
								color: "#F2C94C",
								curve: "linear",
							},
						]}
						xAxis={[{ scaleType: "point", data: xLabels }]}
						yAxis={[{ width: 50 }]}
						margin={{ right: 30 }}
					/>
				)}
			</CardContent>
		</Card>
	);
}
