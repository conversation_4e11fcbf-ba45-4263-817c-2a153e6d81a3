import { Box, Card, CardContent, Stack, Typography } from "@mui/material";
import type * as React from "react";
import { IconWrapper } from "@/shared/components/common/IconWrapper";

export const CustomCard: React.FC<{
	title: string;
	total: number;
	trend: {
		value: number;
		description: string;
		contrastText: string;
		background: string;
	};
	icon: {
		component: React.ComponentType<React.SVGProps<SVGSVGElement>>;
		contrastText: string;
		background: string;
	};
}> = ({ title, total, trend, icon: Icon }) => {
	let trendSymbol = "~";
	if (trend.value > 0) trendSymbol = "▲";
	else if (trend.value < 0) trendSymbol = "▼";

	return (
		<Card sx={{ height: 200 }}>
			<CardContent
				sx={{
					height: "100%",
					display: "flex",
					flexDirection: "column",
					justifyContent: "space-between",
				}}
			>
				{/* Header */}
				<Stack direction="row" spacing={2} alignItems="center">
					<Box
						sx={{
							display: "inline-flex",
							backgroundColor: Icon.background,
							color: Icon.contrastText,
							p: 1,
							borderRadius: 1,
						}}
					>
						<IconWrapper icon={Icon.component} />
					</Box>
					<Typography variant="subtitle1">{title}</Typography>
				</Stack>

				{/* Main Content */}
				<Stack
					direction="row"
					spacing={2}
					alignItems="center"
					justifyContent="space-between"
				>
					<Typography sx={{ fontSize: "3rem" }}>{total}</Typography>
					<Box
						sx={{
							display: "inline-flex",
							backgroundColor: trend.background,
							color: trend.contrastText,
							p: 0.5,
							borderRadius: 1,
						}}
					>
						<Typography variant="subtitle1">
							{trendSymbol} {Math.abs(trend.value)}
						</Typography>
					</Box>
				</Stack>

				{/* Footer */}
				<Box
					sx={{
						backgroundColor: trend.background,
						color: trend.contrastText,
						py: 0.5,
						px: 1,
						borderRadius: 1,
					}}
				>
					<Typography variant="subtitle2">{trend.description}</Typography>
				</Box>
			</CardContent>
		</Card>
	);
};
