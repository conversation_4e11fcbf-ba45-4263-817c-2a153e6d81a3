import {
	Avatar,
	Box,
	Button,
	Chip,
	List,
	ListItem,
	ListItemAvatar,
	ListItemText,
	Paper,
	Typography,
} from "@mui/material";
import type React from "react";

const statusColor = {
	yellow: { backgroundColor: "#fff3cd", color: "#856404" },
	green: { backgroundColor: "#d4edda", color: "#155724" },
	red: { backgroundColor: "#f8d7da", color: "#721c24" },
	blue: { backgroundColor: "#c3e6ff", color: "#0090ff" },
};

export const CurrentActionableItems: React.FC<{
	label: string;
	onViewAll: () => void;
	items: {
		id: string;
		date: string;
		name: string;
		status: string;
		statusColor: string;
		avatar: string;
		captionLabel: string;
		caption: string;
	}[];
}> = ({ items, label, onViewAll }) => {
	return (
		<Paper elevation={1} sx={{ mx: "auto", mt: 2 }}>
			<Box sx={{ p: 2 }}>
				<Typography variant="h6" component="h2" sx={{ mb: 2, fontWeight: 600 }}>
					{label}
				</Typography>

				<List sx={{ p: 0 }}>
					{items.map((item, index) => (
						<ListItem
							key={item.id}
							sx={{
								px: 0,
								py: 1.5,
								borderBottom:
									index < items.length - 1 ? "1px solid #f0f0f0" : "none",
							}}
						>
							<Box
								sx={{ display: "flex", alignItems: "center", width: "100%" }}
							>
								{/* Date */}
								<Typography
									variant="body2"
									sx={{
										minWidth: 80,
										color: "#666",
										fontSize: "0.875rem",
										mr: 2,
									}}
								>
									{item.date}
								</Typography>

								{/* Avatar */}
								<ListItemAvatar sx={{ minWidth: "auto", mr: 2 }}>
									<Avatar
										src={item.avatar}
										sx={{
											width: 40,
											height: 40,
											backgroundColor: "#1976d2",
											fontSize: "1rem",
										}}
									>
										U
									</Avatar>
								</ListItemAvatar>

								{/* Name and Leave Type */}
								<ListItemText
									primary={
										<Typography
											variant="body1"
											sx={{ fontWeight: 500, fontSize: "1rem" }}
										>
											{item.name}
										</Typography>
									}
									secondary={
										<Typography
											variant="body2"
											sx={{
												color: "#666",
												fontSize: "0.875rem",
												fontWeight: 500,
												whiteSpace: "nowrap",
												overflow: "hidden",
												textOverflow: "ellipsis",
											}}
										>
											{item.captionLabel}: {item.caption}
										</Typography>
									}
									sx={{ flex: 1, m: 0 }}
								/>

								{/* Status Chip */}
								<Chip
									label={item.status}
									size="small"
									sx={{
										...statusColor[
											item.statusColor as keyof typeof statusColor
										],
										fontSize: "0.75rem",
										height: 24,
										borderRadius: "12px",
										fontWeight: 500,
									}}
								/>
							</Box>
						</ListItem>
					))}
				</List>

				{/* View All Button */}
				<Box sx={{ mt: 2, textAlign: "center" }}>
					<Button
						onClick={onViewAll}
						fullWidth
						variant="outlined"
						sx={{
							borderColor: "#ffc107",
							color: "#ffc107",
							textTransform: "none",
							borderRadius: 1,
							px: 3,
							"&:hover": {
								borderColor: "#e0a800",
								backgroundColor: "rgba(255, 193, 7, 0.04)",
							},
						}}
					>
						Lihat semua &gt;
					</Button>
				</Box>
			</Box>
		</Paper>
	);
};
