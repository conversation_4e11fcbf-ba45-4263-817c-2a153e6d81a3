import axios, {
	type AxiosInstance,
	type AxiosRequestConfig,
	type AxiosResponse,
} from "axios";

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

let isRefreshing = false;
let failedQueue: Array<{
	resolve: (value?: unknown) => void;
	reject: (reason?: unknown) => void;
}> = [];
const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

function processQueue(error: any, tokenRefreshed: boolean) {
	failedQueue.forEach((prom) => {
		tokenRefreshed ? prom.resolve() : prom.reject(error);
	});
	failedQueue = [];
}

const axiosInstance: AxiosInstance = axios.create({
	baseURL: API_BASE_URL,
	withCredentials: true, // for cookie-based token
	headers: {
		"Content-Type": "application/json",
	},
});

axiosInstance.defaults.headers.common["X-Client-Type"] = "web";
axiosInstance.defaults.headers.common["X-Timezone"] = timezone;

// axiosInstance.interceptors.request.use(
// 	(config: InternalAxiosRequestConfig) => {
// 		config.headers["X-Client-Type"] = "web";
// 		return config;
// 	},
// 	(error) => Promise.reject(error),
// );

axiosInstance.interceptors.response.use(
	(response: AxiosResponse) => response,
	async (error) => {
		const originalRequest = error.config;

		const unhandled401Endpoints = [
			"/api/v1/auth/refresh-token",
			"/api/v1/auth/login",
			"/api/v1/admin/auth/refresh-token",
			"/api/v1/admin/auth/login",
		];

		const is401 =
			error?.response?.status === 401 &&
			!originalRequest?._retry &&
			!unhandled401Endpoints.includes(originalRequest?.url);

		if (!is401) return Promise.reject(error);

		if (isRefreshing) {
			return new Promise((resolve, reject) => {
				failedQueue.push({ resolve, reject });
			})
				.then(() => {
					originalRequest._retry = true;
					return axiosInstance(originalRequest);
				})
				.catch((err) => Promise.reject(err));
		}

		originalRequest._retry = true;
		isRefreshing = true;

		try {
			await axiosInstance.post("/api/v1/auth/refresh-token");

			processQueue(null, true);
			return axiosInstance(originalRequest);
		} catch (refreshError) {
			processQueue(refreshError, false);
			return Promise.reject(refreshError);
		} finally {
			isRefreshing = false;
		}
	},
);

export const apiClient = {
	get: <T = any>(url: string, config?: AxiosRequestConfig) =>
		axiosInstance.get<T>(url, config).then((res) => res.data),

	post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
		axiosInstance.post<T>(url, data, config).then((res) => res.data),

	put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
		axiosInstance.put<T>(url, data, config).then((res) => res.data),

	patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
		axiosInstance.patch<T>(url, data, config).then((res) => res.data),

	delete: <T = any>(url: string, config?: AxiosRequestConfig) =>
		axiosInstance.delete<T>(url, config).then((res) => res.data),

	request: <T = any>(config: AxiosRequestConfig) =>
		axiosInstance.request<T>(config).then((res) => res.data),

	raw: axiosInstance,
};
