import {
	Dashboard,
	Description,
	EmojiEmotions,
	Logout,
	Person,
	Search,
	Settings,
} from "@mui/icons-material";
import {
	<PERSON>,
	Chip,
	Dialog,
	DialogContent,
	Divider,
	InputAdornment,
	ListItemButton,
	ListItemIcon,
	ListItemText,
	TextField,
	Typography,
} from "@mui/material";
import { Command } from "cmdk";
import * as React from "react";

interface CommandPaletteProps {
	open: boolean;
	onClose: () => void;
}

type CommandItem = {
	label: string;
	icon: React.ElementType;
	shortcut?: string;
	action: () => void;
};

type CommandSection = {
	section: string;
	items: CommandItem[];
};

export function CommandPalette({ open, onClose }: CommandPaletteProps) {
	const handleSelect = (action: () => void) => {
		action();
		onClose();
	};

	const commands: CommandSection[] = [
		{
			section: "Suggestions",
			items: [
				{
					label: "Search Emoji",
					icon: EmojiEmotions,
					action: () => alert("Search Emoji"),
				},
			],
		},
		{
			section: "Settings",
			items: [
				{
					label: "Settings",
					icon: Settings,
					shortcut: "⌘S",
					action: () => alert("Go to Settings"),
				},
			],
		},
		{
			section: "Navigation",
			items: [
				{
					label: "Dashboard",
					icon: Dashboard,
					action: () => alert("Go to Dashboard"),
				},
				{
					label: "Profile",
					icon: Person,
					action: () => alert("Go to Profile"),
				},
				{
					label: "Documents",
					icon: Description,
					action: () => alert("Go to Documents"),
				},
			],
		},
		{
			section: "Actions",
			items: [
				{
					label: "Logout",
					icon: Logout,
					action: () => alert("Logout"),
				},
			],
		},
	];

	return (
		<Dialog
			open={open}
			onClose={onClose}
			fullWidth
			maxWidth="sm"
			slotProps={{
				paper: {
					sx: {
						borderRadius: 3,
						overflow: "hidden",
					},
				},
			}}
		>
			<DialogContent sx={{ p: 0 }}>
				<Command
					style={{
						width: "100%",
						display: "flex",
						flexDirection: "column",
						height: "100%",
					}}
				>
					{/* Fixed Search Input */}
					<Box
						sx={{
							borderBottom: 1,
							borderColor: "divider",
							p: 2,
							flexShrink: 0, // biar gak ketarik scroll
							backgroundColor: "background.paper",
							zIndex: 1,
						}}
					>
						<Command.Input asChild>
							<TextField
								variant="outlined"
								autoFocus
								fullWidth
								placeholder="Search or type a command..."
								size="small"
								// InputProps={{
								//   inputComponent: Input,
								// }}
								slotProps={{
									input: {
										startAdornment: (
											<InputAdornment position="start">
												<Search fontSize="small" color="action" />
											</InputAdornment>
										),
									},
								}}
								sx={{
									"& .MuiOutlinedInput-root": {
										borderRadius: 2,
										"& fieldset": {
											border: "none",
										},
									},
								}}
							/>
						</Command.Input>
					</Box>

					{/* Scrollable List */}
					<Box sx={{ flex: 1, overflowY: "auto", maxHeight: 400 }}>
						<Command.List>
							<Command.Empty>
								<Typography
									variant="body2"
									color="text.secondary"
									sx={{
										p: 3,
										textAlign: "center",
									}}
								>
									No results found.
								</Typography>
							</Command.Empty>

							{commands.map((section, idx) => (
								<React.Fragment key={section.section}>
									<Box sx={{ p: 1 }}>
										<Typography
											variant="overline"
											color="text.secondary"
											sx={{
												px: 2,
												py: 1,
												display: "block",
												fontSize: "0.75rem",
												fontWeight: 500,
											}}
										>
											{section.section}
										</Typography>

										{section.items.map((item) => (
											<Command.Item asChild key={item.label}>
												<ListItemButton
													sx={{
														borderRadius: 2,
														mx: 1,
														mb: 0.5,
													}}
													onClick={() => handleSelect(item.action)}
												>
													<ListItemIcon sx={{ minWidth: 36 }}>
														<item.icon fontSize="small" color="action" />
													</ListItemIcon>
													<ListItemText
														primary={item.label}
														slotProps={{
															primary: {
																fontSize: "0.875rem",
															},
														}}
													/>
													{item.shortcut && (
														<Chip
															label={item.shortcut}
															size="small"
															variant="outlined"
															sx={{
																fontSize: "0.65rem",
																height: 20,
																fontFamily: "monospace",
															}}
														/>
													)}
												</ListItemButton>
											</Command.Item>
										))}
									</Box>

									{idx < commands.length - 1 && (
										<Divider sx={{ mx: 2, my: 1 }} />
									)}
								</React.Fragment>
							))}
						</Command.List>
					</Box>
				</Command>
			</DialogContent>
		</Dialog>
	);
}
