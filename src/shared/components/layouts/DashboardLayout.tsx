import MenuIcon from "@mui/icons-material/Menu";
import SearchIcon from "@mui/icons-material/Search";
import {
	Box,
	type CSSObject,
	IconButton,
	InputAdornment,
	Link,
	AppBar as MuiAppBar,
	type AppBarProps as MuiAppBarProps,
	Drawer as <PERSON><PERSON><PERSON><PERSON><PERSON>,
	List as <PERSON><PERSON><PERSON><PERSON>,
	Stack,
	styled,
	TextField,
	type Theme,
	Toolbar,
	Typography,
	useMediaQuery,
} from "@mui/material";
import { useEffect, useState } from "react";
import { Outlet, Link as ReactRouterLink } from "react-router";
import IconsaxSidebarLeftIcon from "@/assets/icons/iconsax-sidebar-left.svg?react";
import GearLogo from "@/assets/logo.webp";
import { useAuth } from "@/shared/hooks";
import type { UserSession } from "@/shared/types/api";
import { AvatarDropdown } from "../common/AvatarDropdown";
import { CommandPalette } from "../common/CommandPallete";
import { IconWrapper } from "../common/IconWrapper";
import { NavListItem } from "./components/NavListItem";
import {
	attendanceNavLists,
	dashboardNavLists,
	leaveNavLists,
	performanceNavLists,
	taskNavLists,
	userNavLists,
} from "./data/navLists";

const drawerWidth = 300;

const openedMixin = (theme: Theme): CSSObject => ({
	width: drawerWidth,
	transition: theme.transitions.create("width", {
		easing: theme.transitions.easing.sharp,
		duration: theme.transitions.duration.enteringScreen,
	}),
	overflowX: "hidden",
});

const closedMixin = (theme: Theme): CSSObject => ({
	transition: theme.transitions.create("width", {
		easing: theme.transitions.easing.sharp,
		duration: theme.transitions.duration.leavingScreen,
	}),
	overflowX: "hidden",
	width: `calc(${theme.spacing(7)} + 1px)`,
	[theme.breakpoints.up("sm")]: {
		width: `calc(${theme.spacing(8)} + 1px)`,
	},
});

const DrawerHeader = styled("div")(({ theme }) => ({
	display: "flex",
	alignItems: "center",
	justifyContent: "space-between",
	padding: theme.spacing(0, 1),
	// necessary for content to be below app bar
	...theme.mixins.toolbar,
}));

interface AppBarProps extends MuiAppBarProps {
	open?: boolean;
}

const AppBar = styled(MuiAppBar, {
	shouldForwardProp: (prop) => prop !== "open",
})<AppBarProps>(({ theme }) => ({
	zIndex: theme.zIndex.drawer + 1,
	transition: theme.transitions.create(["width", "margin"], {
		easing: theme.transitions.easing.sharp,
		duration: theme.transitions.duration.leavingScreen,
	}),
	variants: [
		{
			props: ({ open }) => open,
			style: {
				marginLeft: drawerWidth,
				width: `calc(100% - ${drawerWidth}px)`,
				transition: theme.transitions.create(["width", "margin"], {
					easing: theme.transitions.easing.sharp,
					duration: theme.transitions.duration.enteringScreen,
				}),
			},
		},
	],
}));

const Drawer = styled(MuiDrawer, {
	shouldForwardProp: (prop) => prop !== "open",
})(({ theme }) => ({
	width: drawerWidth,
	flexShrink: 0,
	whiteSpace: "nowrap",
	boxSizing: "border-box",
	variants: [
		{
			props: ({ open }) => open,
			style: {
				...openedMixin(theme),
				"& .MuiDrawer-paper": openedMixin(theme),
				border: "none",
			},
		},
		{
			props: ({ open }) => !open,
			style: {
				...closedMixin(theme),
				"& .MuiDrawer-paper": closedMixin(theme),
				border: "none",
			},
		},
	],
}));

export const DashboardLayout: React.FC = () => {
	const { user, logout } = useAuth();
	const isMobile = useMediaQuery((theme: Theme) =>
		theme.breakpoints.down("sm"),
	);
	const [open, setOpen] = useState(!isMobile);
	const [openCmdk, setOpenCmdk] = useState(false);

	const handleDrawerOpen = () => {
		setOpen(true);
	};

	const handleDrawerClose = () => {
		setOpen(false);
	};

	useEffect(() => {
		const down = (e: KeyboardEvent) => {
			if ((e.metaKey || e.ctrlKey) && e.key.toLowerCase() === "k") {
				e.preventDefault();
				setOpenCmdk(true);
			}
		};
		document.addEventListener("keydown", down);
		return () => document.removeEventListener("keydown", down);
	}, []);

	return (
		<Box sx={{ display: "flex" }}>
			<AppBar elevation={0} position="fixed" color="inherit" open={open}>
				<Toolbar>
					<IconButton
						color="inherit"
						aria-label="open drawer"
						onClick={handleDrawerOpen}
						edge="start"
						sx={[
							{
								marginRight: 5,
							},
							open && { display: "none" },
						]}
					>
						<MenuIcon />
					</IconButton>

					<Stack
						direction="row"
						justifyContent="space-between"
						alignItems="center"
						sx={{ flexGrow: 1 }}
					>
						{/* <TextField
							variant="outlined"
							size="small"
							// value={value}
							// onChange={onChange}
							placeholder={"Cari disini..."}
							sx={{
								flexGrow: 1,
								width: "100%",
								maxWidth: 300,
								backgroundColor: "#fff",
								"& .MuiOutlinedInput-root": {
									"&:hover .MuiOutlinedInput-notchedOutline": {
										borderColor: "#BDBDBD", // grey when hover
									},
									"&.Mui-focused .MuiOutlinedInput-notchedOutline": {
										borderColor: "primary.main", // use default or set your focus color here
									},
								},
							}}
							slotProps={{
								input: {
									startAdornment: (
										<InputAdornment position="start">
											<SearchIcon sx={{ color: "#888888" }} />
										</InputAdornment>
									),
								},
							}}
						/> */}
						<CommandPalette
							open={openCmdk}
							onClose={() => setOpenCmdk(false)}
						/>
						<TextField
							placeholder="Search (Ctrl+K)"
							fullWidth
							size="small"
							variant="outlined"
							onClick={() => setOpenCmdk(true)}
							slotProps={{
								input: {
									startAdornment: (
										<InputAdornment position="start">
											<SearchIcon />
										</InputAdornment>
									),
								},
							}}
							sx={{
								flexGrow: 1,
								width: "100%",
								maxWidth: 300,
								backgroundColor: "#fff",
								"& .MuiOutlinedInput-root": {
									"&:hover .MuiOutlinedInput-notchedOutline": {
										borderColor: "#BDBDBD", // grey when hover
									},
									"&.Mui-focused .MuiOutlinedInput-notchedOutline": {
										borderColor: "primary.main", // use default or set your focus color here
									},
								},
							}}
						/>
						<AvatarDropdown
							userSession={user as UserSession}
							onLogout={() => logout()}
							onProfile={() => {}}
						/>
					</Stack>
				</Toolbar>
			</AppBar>
			<Drawer
				variant="permanent"
				open={open}
				sx={{
					"& .MuiDrawer-paper": {
						border: "none",
					},
				}}
				slotProps={{
					paper: {
						sx: {
							overflowY: "auto",
							"&::-webkit-scrollbar": {
								width: "0px",
							},
							"&::-webkit-scrollbar-thumb": {
								backgroundColor: "transparent",
								borderRadius: "4px",
							},
							"&::-webkit-scrollbar-track": {
								backgroundColor: "transparent",
							},
						},
					},
				}}
			>
				<DrawerHeader sx={{ px: 3 }}>
					<Link
						component={ReactRouterLink}
						to="/dashboard"
						underline="none"
						sx={{
							display: "flex",
							alignItems: "center",
							justifyContent: "center",
							pl: 1,
						}}
					>
						<Box
							component="img"
							src={GearLogo}
							alt="Logo Gear"
							sx={{ width: 30, height: 30, mr: 1, objectFit: "cover" }}
						/>
						<Box sx={{ lineHeight: 1 }}>
							<Typography
								sx={{
									fontWeight: 900,
									fontSize: "1.5rem",
									letterSpacing: "0.1em",
									color: "text.primary",
									lineHeight: 1,
								}}
							>
								PMCK
							</Typography>
						</Box>
					</Link>
					<IconButton onClick={handleDrawerClose}>
						<IconWrapper icon={IconsaxSidebarLeftIcon} />
					</IconButton>
				</DrawerHeader>
				<MuiList sx={[open ? { px: 2 } : { px: 1 }]}>
					{dashboardNavLists.map((list) => (
						<NavListItem key={list.id} item={list} open={open} />
					))}

					{open && (
						<Typography
							variant="body2"
							sx={{ pl: 2, mt: 2, color: "text.disabled" }}
						>
							User
						</Typography>
					)}
					{userNavLists.map((list) => (
						<NavListItem key={list.id} item={list} open={open} />
					))}

					{open && (
						<Typography
							variant="body2"
							sx={{ pl: 2, mt: 2, color: "text.disabled" }}
						>
							Kehadiran
						</Typography>
					)}
					{attendanceNavLists.map((list) => (
						<NavListItem key={list.id} item={list} open={open} />
					))}

					{open && (
						<Typography
							variant="body2"
							sx={{ pl: 2, mt: 2, color: "text.disabled" }}
						>
							Cuti
						</Typography>
					)}
					{leaveNavLists.map((list) => (
						<NavListItem key={list.id} item={list} open={open} />
					))}

					{open && (
						<Typography
							variant="body2"
							sx={{ pl: 2, mt: 2, color: "text.disabled" }}
						>
							Tugas
						</Typography>
					)}
					{taskNavLists.map((list) => (
						<NavListItem key={list.id} item={list} open={open} />
					))}

					{open && (
						<Typography
							variant="body2"
							sx={{ pl: 2, mt: 2, color: "text.disabled" }}
						>
							Performa
						</Typography>
					)}
					{performanceNavLists.map((list) => (
						<NavListItem key={list.id} item={list} open={open} />
					))}
				</MuiList>
			</Drawer>
			<Box
				component="main"
				sx={{
					flexGrow: 1,
					height: "100vh",
					overflow: "hidden",
				}}
			>
				<Box
					sx={{
						height: "100%",
						backgroundColor: "#FAFAFA",
						mt: 9,
						borderTopLeftRadius: "16px",
						overflow: "hidden",
						display: "flex",
						flexDirection: "column",
					}}
				>
					{/* Ini bagian scrollable-nya */}
					<Box
						sx={{
							flex: 1,
							overflowY: "auto",
							px: 4,
							py: 3,
						}}
					>
						<Outlet />
					</Box>
				</Box>
			</Box>
		</Box>
	);
};
