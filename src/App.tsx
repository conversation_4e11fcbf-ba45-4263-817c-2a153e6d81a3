import { Box } from "@mui/material";
import { lazy, Suspense } from "react";
import { createBrowser<PERSON>outer, Navigate, RouterProvider } from "react-router";
import FullPageLoader from "./shared/components/common/FullPageLoader";
import { AppLayout } from "./shared/components/layouts/AppLayout";
import { DashboardLayout } from "./shared/components/layouts/DashboardLayout";
import { AuthRouteGuard } from "./shared/components/routes/AuthRouteGuard";
import { ProtectedRouteGuard } from "./shared/components/routes/ProtectedRouteGuard";
import NotFoundPage from "./shared/pages/404";

// Auth
const LoginPage = lazy(() => import("./modules/auth/pages/LoginPage"));
const ForgotPasswordPage = lazy(
	() => import("./modules/auth/pages/ForgotPasswordPage"),
);
const ResetPasswordPage = lazy(
	() => import("./modules/auth/pages/ResetPasswordPage"),
);

const DashboardPage = lazy(
	() => import("./modules/dashboard/pages/DashboardPage"),
);

// User pages
const UserListPage = lazy(() => import("./modules/users/pages/UserListPage"));
const UserTreePage = lazy(() => import("./modules/users/pages/UserTreePage"));
const EditUserPage = lazy(() => import("./modules/users/pages/EditUserPage"));
const AdministratorUserListPage = lazy(
	() => import("./modules/users/pages/AdministratorUserListPage"),
);
const CreateAdministratorUserPage = lazy(
	() => import("./modules/users/pages/CreateAdministratorUserPage"),
);
const CreateNewUserPage = lazy(
	() => import("./modules/users/pages/CreateUserPage"),
);
const EditAdministratorUserPage = lazy(
	() => import("./modules/users/pages/EditAdministratorUserPage"),
);

// Role/Organization pages
const RoleListPage = lazy(() => import("./modules/roles/pages/RoleListPage"));
const CreateRolePage = lazy(
	() => import("./modules/roles/pages/CreateRolePage"),
);
const RoleTreePage = lazy(() => import("./modules/roles/pages/RoleTreePage"));
const EditRolePage = lazy(() => import("./modules/roles/pages/EditRolePage"));

// Attendance pages
const AttendanceLogListPage = lazy(
	() => import("./modules/attendance/pages/AttendanceLogListPage"),
);
const CreateAttendanceLogPage = lazy(
	() => import("./modules/attendance/pages/CreateAttendanceLogPage"),
);

const EditAttendanceLogPage = lazy(
	() => import("./modules/attendance/pages/EditAttendanceLogPage"),
);

const AttendanceConfigurationPage = lazy(
	() => import("./modules/attendance/pages/AttendanceConfigurationPage"),
);

// Worksite pages
const WorksiteListPage = lazy(() =>
	import("./modules/worksite/pages").then((module) => ({
		default: module.WorksiteListPage,
	})),
);
const CreateWorksitePage = lazy(() =>
	import("./modules/worksite/pages").then((module) => ({
		default: module.CreateWorksitePage,
	})),
);
const EditWorksitePage = lazy(() =>
	import("./modules/worksite/pages").then((module) => ({
		default: module.EditWorksitePage,
	})),
);

// Holiday pages
const HolidayListPage = lazy(
	() => import("./modules/holiday/pages/HolidayListPage"),
);
const CreateHolidayPage = lazy(
	() => import("./modules/holiday/pages/CreateHolidayPage"),
);
const EditHolidayPage = lazy(
	() => import("./modules/holiday/pages/EditHolidayPage"),
);

// Office Leave pages
const OfficeLeaveListPage = lazy(
	() => import("./modules/office-leave/pages/OfficeLeaveListPage"),
);
const CreateOfficeLeavePage = lazy(
	() => import("./modules/office-leave/pages/CreateOfficeLeavePage"),
);
const EditOfficeLeavePage = lazy(
	() => import("./modules/office-leave/pages/EditOfficeLeavePage"),
);

// Leave Request pages
const LeaveRequestListPage = lazy(
	() => import("./modules/leave/pages/LeaveRequestListPage"),
);
const CreateLeaveRequestPage = lazy(
	() => import("./modules/leave/pages/CreateLeaveRequestPage"),
);
const EditLeaveRequestPage = lazy(
	() => import("./modules/leave/pages/EditLeaveRequestPage"),
);

// Leave Policy pages
const LeavePolicyListPage = lazy(
	() => import("./modules/leave-policy/pages/LeavePolicyListPage"),
);
const CreateLeavePolicyPage = lazy(
	() => import("./modules/leave-policy/pages/CreateLeavePolicyPage"),
);
const EditLeavePolicyPage = lazy(
	() => import("./modules/leave-policy/pages/EditLeavePolicyPage"),
);

// Task pages
const TaskListPage = lazy(() => import("./modules/task/pages/TaskListPage"));
const CreateTaskPage = lazy(
	() => import("./modules/task/pages/CreateTaskPage"),
);
const EditTaskPage = lazy(() => import("./modules/task/pages/EditTaskPage"));

// Violation pages
const ViolationListPage = lazy(() =>
	import("./modules/violation/pages").then((module) => ({
		default: module.ViolationListPage,
	})),
);
const CreateViolationPage = lazy(() =>
	import("./modules/violation/pages").then((module) => ({
		default: module.CreateViolationPage,
	})),
);
const EditViolationPage = lazy(() =>
	import("./modules/violation/pages").then((module) => ({
		default: module.EditViolationPage,
	})),
);

// Violation Type pages
const ViolationTypeListPage = lazy(() =>
	import("./modules/violation-types/pages").then((module) => ({
		default: module.ViolationTypeListPage,
	})),
);
const CreateViolationTypePage = lazy(() =>
	import("./modules/violation-types/pages").then((module) => ({
		default: module.CreateViolationTypePage,
	})),
);
const EditViolationTypePage = lazy(() =>
	import("./modules/violation-types/pages").then((module) => ({
		default: module.EditViolationTypePage,
	})),
);

// Other pages
const IncentiveListPage = lazy(
	() => import("./modules/incentive/pages/IncentiveListPage"),
);
const KpiRecordListPage = lazy(
	() => import("./modules/kpi-record/pages/KpiRecordListPage"),
);
const AttendanceMonitoringPage = lazy(
	() => import("./modules/debug/pages/AttendanceMonitoringPage"),
);

const SuspenseWrapper = ({ children }: { children: React.ReactNode }) => (
	<Suspense fallback={<FullPageLoader />}>{children}</Suspense>
);

const router = createBrowserRouter([
	{
		element: <AppLayout />,
		children: [
			{
				path: "/",
				element: <Navigate to="/dashboard" />,
			},
			{
				element: <AuthRouteGuard />,
				children: [
					{
						path: "/auth/login",
						element: (
							<SuspenseWrapper>
								<LoginPage />
							</SuspenseWrapper>
						),
					},
					{
						path: "/auth/forgot-password",
						element: (
							<SuspenseWrapper>
								<ForgotPasswordPage />
							</SuspenseWrapper>
						),
					},
					{
						path: "/auth/reset-password",
						element: (
							<SuspenseWrapper>
								<ResetPasswordPage />
							</SuspenseWrapper>
						),
					},
				],
			},
			{
				element: <ProtectedRouteGuard />,
				children: [
					{
						element: <DashboardLayout />,
						children: [
							{
								index: true,
								path: "/dashboard",
								element: (
									<SuspenseWrapper>
										<DashboardPage />
									</SuspenseWrapper>
								),
							},
							{
								path: "/users",
								children: [
									{
										index: true,
										element: (
											<SuspenseWrapper>
												<UserListPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/users/administrators",
										element: (
											<SuspenseWrapper>
												<AdministratorUserListPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/users/hierarchy",
										element: (
											<SuspenseWrapper>
												<UserTreePage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/users/new",
										element: (
											<SuspenseWrapper>
												<CreateNewUserPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/users/administrators/new",
										element: (
											<SuspenseWrapper>
												<CreateAdministratorUserPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/users/:userId/edit",
										element: (
											<SuspenseWrapper>
												<EditUserPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/users/administrators/:administratorUserId/edit",
										element: (
											<SuspenseWrapper>
												<EditAdministratorUserPage />
											</SuspenseWrapper>
										),
									},
								],
							},

							{
								path: "/organizations",
								children: [
									{
										index: true,
										element: (
											<SuspenseWrapper>
												<RoleListPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/organizations/new",
										element: (
											<SuspenseWrapper>
												<CreateRolePage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/organizations/hierarchy",
										element: (
											<SuspenseWrapper>
												<RoleTreePage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/organizations/:roleId/edit",
										element: (
											<SuspenseWrapper>
												<EditRolePage />
											</SuspenseWrapper>
										),
									},
								],
							},
							{
								path: "/attendance",
								children: [
									{
										index: true,
										element: (
											<SuspenseWrapper>
												<AttendanceLogListPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/attendance/new",
										element: (
											<SuspenseWrapper>
												<CreateAttendanceLogPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/attendance/:attendanceLogId/edit",
										element: (
											<SuspenseWrapper>
												<EditAttendanceLogPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/attendance/configurations",
										element: (
											<SuspenseWrapper>
												<AttendanceConfigurationPage />
											</SuspenseWrapper>
										),
									},
								],
							},
							{
								path: "/worksites",
								children: [
									{
										index: true,
										element: (
											<SuspenseWrapper>
												<WorksiteListPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/worksites/new",
										element: (
											<SuspenseWrapper>
												<CreateWorksitePage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/worksites/:worksiteId/edit",
										element: (
											<SuspenseWrapper>
												<EditWorksitePage />
											</SuspenseWrapper>
										),
									},
								],
							},
							{
								path: "/holidays",
								children: [
									{
										index: true,
										element: (
											<SuspenseWrapper>
												<HolidayListPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/holidays/new",
										element: (
											<SuspenseWrapper>
												<CreateHolidayPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/holidays/:holidayId/edit",
										element: (
											<SuspenseWrapper>
												<EditHolidayPage />
											</SuspenseWrapper>
										),
									},
								],
							},
							{
								path: "/office-leaves",
								children: [
									{
										index: true,
										element: (
											<SuspenseWrapper>
												<OfficeLeaveListPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/office-leaves/new",
										element: (
											<SuspenseWrapper>
												<CreateOfficeLeavePage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/office-leaves/:officeLeaveId/edit",
										element: (
											<SuspenseWrapper>
												<EditOfficeLeavePage />
											</SuspenseWrapper>
										),
									},
								],
							},
							{
								path: "/leaves",
								children: [
									{
										index: true,
										element: (
											<SuspenseWrapper>
												<LeaveRequestListPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/leaves/new",
										element: (
											<SuspenseWrapper>
												<CreateLeaveRequestPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/leaves/:leaveRequestId/edit",
										element: (
											<SuspenseWrapper>
												<EditLeaveRequestPage />
											</SuspenseWrapper>
										),
									},
								],
							},
							{
								path: "/leave-policies",
								children: [
									{
										index: true,
										element: (
											<SuspenseWrapper>
												<LeavePolicyListPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/leave-policies/new",
										element: (
											<SuspenseWrapper>
												<CreateLeavePolicyPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/leave-policies/:leavePolicyId/edit",
										element: (
											<SuspenseWrapper>
												<EditLeavePolicyPage />
											</SuspenseWrapper>
										),
									},
								],
							},
							{
								path: "/tasks",
								children: [
									{
										index: true,
										element: (
											<SuspenseWrapper>
												<TaskListPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/tasks/new",
										element: (
											<SuspenseWrapper>
												<CreateTaskPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/tasks/:taskId/edit",
										element: (
											<SuspenseWrapper>
												<EditTaskPage />
											</SuspenseWrapper>
										),
									},
								],
							},
							{
								path: "/violations",
								children: [
									{
										index: true,
										element: (
											<SuspenseWrapper>
												<ViolationListPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/violations/new",
										element: (
											<SuspenseWrapper>
												<CreateViolationPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/violations/:violationId/edit",
										element: (
											<SuspenseWrapper>
												<EditViolationPage />
											</SuspenseWrapper>
										),
									},
								],
							},
							{
								path: "/violation-types",
								children: [
									{
										index: true,
										element: (
											<SuspenseWrapper>
												<ViolationTypeListPage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/violation-types/new",
										element: (
											<SuspenseWrapper>
												<CreateViolationTypePage />
											</SuspenseWrapper>
										),
									},
									{
										path: "/violation-types/:violationTypeId/edit",
										element: (
											<SuspenseWrapper>
												<EditViolationTypePage />
											</SuspenseWrapper>
										),
									},
								],
							},
							{
								path: "/incentives",
								children: [
									{
										index: true,
										element: (
											<SuspenseWrapper>
												<IncentiveListPage />
											</SuspenseWrapper>
										),
									},
								],
							},
							{
								path: "/kpi",
								children: [
									{
										index: true,
										element: (
											<SuspenseWrapper>
												<KpiRecordListPage />
											</SuspenseWrapper>
										),
									},
								],
							},
						],
					},
					{
						path: "/debug/attendance-monitoring",
						element: (
							<SuspenseWrapper>
								<AttendanceMonitoringPage />
							</SuspenseWrapper>
						),
					},
				],
			},
			{
				path: "*",
				element: <NotFoundPage />,
			},
		],
	},
]);

const App: React.FC = () => {
	return (
		<Box sx={{ minHeight: "100vh", backgroundColor: "background.default" }}>
			<RouterProvider router={router} />
		</Box>
	);
};

export default App;
